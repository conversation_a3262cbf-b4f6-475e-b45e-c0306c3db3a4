import { useState } from 'react';
import clsx from 'clsx';
import Link from '@docusaurus/Link';
import Layout from '@theme/Layout';
import Translate, { translate } from '@docusaurus/Translate';
import Heading from '@theme/Heading';

import extensionsStyles from './ai-extensions.module.css';
import Footer from '../components/Footer';
import FAQSection from '../components/FAQSection';
import ImageModal from '../components/ImageModal';
import GoogleAccountAnalytics from '../components/GoogleAccountAnalytics';
import CTASection from '../components/CTASection';
import ExtensionStructuredData from '../components/ExtensionStructuredData';
import SocialProofSection from '../components/SocialProofSection';

function UniqueValueSection() {
  return (
    <section style={{ backgroundColor: '#f8f9fa', padding: '5rem 0' }}>
      <div className="container">
        <div className={extensionsStyles.sectionHeading}>
          <Heading as="h2" className={extensionsStyles.sectionTitle}>
            <Translate id="ai_extensions.unique_value.title">
              Why Choose FunBlocks AI Extensions?
            </Translate>
          </Heading>
          <p className={extensionsStyles.sectionDescription}>
            <Translate id="ai_extensions.unique_value.subtitle">
              Beyond task completion - we focus on cultivating your higher-order thinking abilities
            </Translate>
          </p>
        </div>

        {/* Core Philosophy - Full Width */}
        <div style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: '16px',
          padding: '3rem',
          color: 'white',
          marginTop: '3rem',
          marginBottom: '3rem',
          textAlign: 'center'
        }}>
          <div style={{
            background: 'rgba(255, 255, 255, 0.2)',
            borderRadius: '50%',
            width: '80px',
            height: '80px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 2rem',
            fontSize: '36px'
          }}>
            💡
          </div>
          <Heading as="h3" style={{ color: 'white', marginBottom: '1.5rem', fontSize: '2rem' }}>
            <Translate id="ai_extensions.unique_value.philosophy_title">
              AI Enhances Human Intelligence, Not Replaces It
            </Translate>
          </Heading>
          <p style={{ fontSize: '1.2rem', opacity: 0.9, maxWidth: '800px', margin: '0 auto' }}>
            <Translate id="ai_extensions.unique_value.philosophy_description">
              While other extensions focus on quick task completion, FunBlocks AI Extensions prioritize developing your critical thinking and cognitive abilities for long-term growth.
            </Translate>
          </p>
        </div>

        {/* Three Feature Cards - Equal Width in Same Row */}
        <div className={extensionsStyles.benefitsGrid}>
          {/* Comprehensive Information Presentation */}
          <div className="col-lg-4 col-md-4 mb-4">
            <div style={{
              background: 'white',
              borderRadius: '12px',
              padding: '2rem',
              height: '100%',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
              border: '1px solid #e5e7eb'
            }}>
              <div style={{
                background: 'linear-gradient(135deg, #4ade80 0%, #22c55e 100%)',
                borderRadius: '50%',
                width: '60px',
                height: '60px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '1.5rem',
                fontSize: '24px'
              }}>
                📊
              </div>
              <Heading as="h4" style={{ marginBottom: '1rem', color: '#1f2937' }}>
                <Translate id="ai_extensions.unique_value.comprehensive_title">
                  Comprehensive & Intuitive Information Presentation
                </Translate>
              </Heading>
              <p style={{ color: '#6b7280', lineHeight: '1.6' }}>
                <Translate id="ai_extensions.unique_value.comprehensive_description">
                  Transform complex information into visual mind maps, structured frameworks, and interactive presentations that make understanding effortless and comprehensive.
                </Translate>
              </p>
            </div>
          </div>

          {/* Multi-Perspective Exploration */}
          <div className="col-lg-4 col-md-4 mb-4">
            <div style={{
              background: 'white',
              borderRadius: '12px',
              padding: '2rem',
              height: '100%',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
              border: '1px solid #e5e7eb'
            }}>
              <div style={{
                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                borderRadius: '50%',
                width: '60px',
                height: '60px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '1.5rem',
                fontSize: '24px'
              }}>
                🔍
              </div>
              <Heading as="h4" style={{ marginBottom: '1rem', color: '#1f2937' }}>
                <Translate id="ai_extensions.unique_value.multiperspective_title">
                  Multi-Perspective Deep Exploration
                </Translate>
              </Heading>
              <p style={{ color: '#6b7280', lineHeight: '1.6' }}>
                <Translate id="ai_extensions.unique_value.multiperspective_description">
                  Explore topics from multiple angles using structured thinking frameworks like Six Thinking Hats and SWOT Analysis, preventing information silos and echo chambers.
                </Translate>
              </p>
            </div>
          </div>

          {/* Critical Analysis Tools */}
          <div className="col-lg-4 col-md-4 mb-4">
            <div style={{
              background: 'white',
              borderRadius: '12px',
              padding: '2rem',
              height: '100%',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
              border: '1px solid #e5e7eb'
            }}>
              <div style={{
                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                borderRadius: '50%',
                width: '60px',
                height: '60px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '1.5rem',
                fontSize: '24px'
              }}>
                ⚖️
              </div>
              <Heading as="h4" style={{ marginBottom: '1rem', color: '#1f2937' }}>
                <Translate id="ai_extensions.unique_value.critical_analysis_title">
                  Advanced Critical Analysis Capabilities
                </Translate>
              </Heading>
              <p style={{ color: '#6b7280', lineHeight: '1.6' }}>
                <Translate id="ai_extensions.unique_value.critical_analysis_description">
                  Detect biases, generate counterexamples, and perform comprehensive critical analysis that other extensions simply cannot provide.
                </Translate>
              </p>
            </div>
          </div>
        </div>

        {/* Unique Features List */}
        <div style={{
          // background: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
          // backgroundColor: 'deepskyblue',
          borderRadius: '16px',
          padding: '3rem',
          marginTop: '3rem',
          color: 'white'
        }}>
          <Heading as="h3" style={{ color: 'white', marginBottom: '2rem', textAlign: 'center' }}>
            <Translate id="ai_extensions.unique_value.features_title">
              Exclusive Features You Won't Find Elsewhere
            </Translate>
          </Heading>

          <div className="row">
            <div className="col-lg-6">
              <ul style={{ listStyle: 'none', padding: 0 }}>
                <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '1rem' }}>
                  <span style={{ marginRight: '1rem', color: 'gold', fontSize: '1.2rem' }}>✓</span>
                  <div>
                    <strong><Translate id="ai_extensions.unique_value.feature1_title">Bias Detection & Analysis</Translate></strong>
                    <br />
                    <span style={{ opacity: 0.9 }}>
                      <Translate id="ai_extensions.unique_value.feature1_description">
                        Automatically identify potential biases in content and AI responses
                      </Translate>
                    </span>
                  </div>
                </li>
                <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '1rem' }}>
                  <span style={{ marginRight: '1rem', color: 'gold', fontSize: '1.2rem' }}>✓</span>
                  <div>
                    <strong><Translate id="ai_extensions.unique_value.feature2_title">Counterexample Generation</Translate></strong>
                    <br />
                    <span style={{ opacity: 0.9 }}>
                      <Translate id="ai_extensions.unique_value.feature2_description">
                        Generate opposing viewpoints and counterexamples to strengthen critical thinking
                      </Translate>
                    </span>
                  </div>
                </li>
                <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '1rem' }}>
                  <span style={{ marginRight: '1rem', color: 'gold', fontSize: '1.2rem' }}>✓</span>
                  <div>
                    <strong><Translate id="ai_extensions.unique_value.feature3_title">Structured Thinking Frameworks</Translate></strong>
                    <br />
                    <span style={{ opacity: 0.9 }}>
                      <Translate id="ai_extensions.unique_value.feature3_description">
                        Apply proven methodologies like Six Thinking Hats, SWOT, and First Principles
                      </Translate>
                    </span>
                  </div>
                </li>
              </ul>
            </div>
            <div className="col-lg-6">
              <ul style={{ listStyle: 'none', padding: 0 }}>
                <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '1rem' }}>
                  <span style={{ marginRight: '1rem', color: 'gold', fontSize: '1.2rem' }}>✓</span>
                  <div>
                    <strong><Translate id="ai_extensions.unique_value.feature4_title">Visual Mind Mapping</Translate></strong>
                    <br />
                    <span style={{ opacity: 0.9 }}>
                      <Translate id="ai_extensions.unique_value.feature4_description">
                        Transform linear text into comprehensive visual knowledge structures
                      </Translate>
                    </span>
                  </div>
                </li>
                <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '1rem' }}>
                  <span style={{ marginRight: '1rem', color: 'gold', fontSize: '1.2rem' }}>✓</span>
                  <div>
                    <strong><Translate id="ai_extensions.unique_value.feature5_title">Comprehensive Critical Analysis</Translate></strong>
                    <br />
                    <span style={{ opacity: 0.9 }}>
                      <Translate id="ai_extensions.unique_value.feature5_description">
                        Perform deep analytical thinking that goes beyond surface-level responses
                      </Translate>
                    </span>
                  </div>
                </li>
                <li style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '1rem' }}>
                  <span style={{ marginRight: '1rem', color: 'gold', fontSize: '1.2rem' }}>✓</span>
                  <div>
                    <strong><Translate id="ai_extensions.unique_value.feature6_title">Anti-Echo Chamber Technology</Translate></strong>
                    <br />
                    <span style={{ opacity: 0.9 }}>
                      <Translate id="ai_extensions.unique_value.feature6_description">
                        Actively prevent information silos by presenting diverse perspectives
                      </Translate>
                    </span>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div style={{ textAlign: 'center', marginTop: '3rem' }}>
          <p style={{ fontSize: '1.1rem', color: '#6b7280', marginBottom: '2rem' }}>
            <Translate id="ai_extensions.unique_value.cta_description">
              Experience the difference that true cognitive enhancement makes. Choose FunBlocks AI Extensions for thinking that matters in the AI era.
            </Translate>
          </p>
        </div>
      </div>
    </section>
  );
}

function ExtensionsHeader({ setShowImageSrc }) {
  return (
    <section id="hero" className={clsx(extensionsStyles.hero, extensionsStyles.pageSection)} style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <div className="container">
        <div className={extensionsStyles.heroRow}>
          <div className={extensionsStyles.heroContent} style={{ flex: 1, minWidth: 0 }}>
            <div className={extensionsStyles.heroBadge}>
              <Translate id="ai_extensions.hero.badge">
                BROWSER EXTENSIONS
              </Translate>
            </div>
            <Heading as="h1">
              <Translate id="ai_extensions.hero.title">
                FunBlocks AI Browser Extensions
              </Translate>
            </Heading>
            <p className={extensionsStyles.heroSubtitle}>
              <Translate id="ai_extensions.hero.subtitle">
                Enhance your browsing experience with AI-powered reading, writing, and thinking tools. Available anytime, anywhere on the web.
              </Translate>
            </p>

            <div className={extensionsStyles.heroButtons}>
              <Link
                className={clsx('button', extensionsStyles.btnPrimary)}
                to="#extensions-overview"
              >
                <Translate id="ai_extensions.hero.explore">Explore Extensions</Translate>
              </Link>
              <Link
                className={clsx('button', extensionsStyles.btnSecondary)}
                to="/"
              >
                <Translate id="ai_extensions.hero.learn_more">Learn More</Translate>
              </Link>
            </div>

            <div className={extensionsStyles.heroStats}>
              <div className={extensionsStyles.heroStat}>
                <span className={extensionsStyles.heroStatNumber}>3</span>
                <span className={extensionsStyles.heroStatLabel}>
                  <Translate id="ai_extensions.hero.stat1">Powerful Extensions</Translate>
                </span>
              </div>
              <div className={extensionsStyles.heroStat}>
                <span className={extensionsStyles.heroStatNumber}>100K+</span>
                <span className={extensionsStyles.heroStatLabel}>
                  <Translate id="ai_extensions.hero.stat2">Active Users</Translate>
                </span>
              </div>
              <div className={extensionsStyles.heroStat}>
                <span className={extensionsStyles.heroStatNumber}>4.8★</span>
                <span className={extensionsStyles.heroStatLabel}>
                  <Translate id="ai_extensions.hero.stat3">User Rating</Translate>
                </span>
              </div>
            </div>
          </div>
          <div className={extensionsStyles.heroImageContainer} style={{ flex: 1, minWidth: 0, display: 'flex', justifyContent: 'center' }}>
            <div className={extensionsStyles.heroImageWrapper}>
              <img
                className={extensionsStyles.heroImage}
                onClick={() => setShowImageSrc("/img/portfolio/fullsize/all_in_one_en.png")}
                id="ai-extensions-overview"
                alt="FunBlocks AI Browser Extensions overview"
                src="/img/portfolio/fullsize/all_in_one_en.png"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function ExtensionsBenefits() {
  const benefits = [
    {
      icon: '⚡',
      title: translate({
        id: 'ai_extensions.benefits.convenience.title',
        message: 'Always Available Convenience'
      }),
      description: translate({
        id: 'ai_extensions.benefits.convenience.description',
        message: 'Access powerful AI tools instantly while browsing any website or working on any task, without switching between applications.'
      })
    },
    {
      icon: '🔄',
      title: translate({
        id: 'ai_extensions.benefits.context.title',
        message: 'Automatic Context Capture'
      }),
      description: translate({
        id: 'ai_extensions.benefits.context.description',
        message: 'Automatically capture webpage content as context for AI assistance, eliminating the need for manual copy-pasting.'
      })
    },
    {
      icon: '🚀',
      title: translate({
        id: 'ai_extensions.benefits.efficiency.title',
        message: 'Dramatically Improved Efficiency'
      }),
      description: translate({
        id: 'ai_extensions.benefits.efficiency.description',
        message: 'Process web content with AI assistance to boost productivity by 10x in reading, writing, and thinking tasks.'
      })
    },
    {
      icon: '💡',
      title: translate({
        id: 'ai_extensions.benefits.thinking.title',
        message: 'Enhanced Critical Thinking'
      }),
      description: translate({
        id: 'ai_extensions.benefits.thinking.description',
        message: 'Develop stronger analytical skills with AI-powered critical thinking frameworks and structured analysis tools.'
      })
    }
  ];

  return (
    <section className={extensionsStyles.benefitsSection}>
      <div className="container">
        <div className={extensionsStyles.sectionHeading}>
          <Heading as="h2" className={extensionsStyles.sectionTitle}>
            <Translate id="ai_extensions.benefits.title">Key Benefits</Translate>
          </Heading>
          <p className={extensionsStyles.sectionDescription}>
            <Translate id="ai_extensions.benefits.description">
              Discover how FunBlocks AI Browser Extensions transform your daily web experience with powerful AI capabilities.
            </Translate>
          </p>
        </div>

        <div className={extensionsStyles.benefitsGrid}>
          {benefits.map((benefit, index) => (
            <div key={index} className={extensionsStyles.benefitCard}>
              <div className={extensionsStyles.benefitIcon}>{benefit.icon}</div>
              <Heading as="h3" className={extensionsStyles.benefitTitle}>
                {benefit.title}
              </Heading>
              <p className={extensionsStyles.benefitDescription}>
                {benefit.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

function ExtensionsOverview({ setShowImageSrc }) {
  const extensions = [
    {
      id: 'funblocks-ai-extension',
      badge: 'COMPREHENSIVE',
      icon: '/img/icon.png',
      title: translate({
        id: 'ai_extensions.overview.extension1.title',
        message: 'FunBlocks AI Assistant'
      }),
      subtitle: translate({
        id: 'ai_extensions.overview.extension1.subtitle',
        message: 'Complete AI-powered browsing assistant with writing, reading, and thinking tools'
      }),
      description: translate({
        id: 'ai_extensions.overview.extension1.description',
        message: 'The most comprehensive AI browser extension that includes all features from our other extensions plus advanced writing assistance, contextual AI tools, and seamless integration with FunBlocks AIFlow.'
      }),
      features: [
        translate({
          id: 'ai_extensions.overview.extension1.feature1',
          message: 'AI Writing Assistant with contextual toolbar'
        }),
        translate({
          id: 'ai_extensions.overview.extension1.feature2',
          message: 'AI Reading Assistant with critical thinking frameworks'
        }),
        translate({
          id: 'ai_extensions.overview.extension1.feature3',
          message: 'Mind mapping and brainstorming tools'
        }),
        translate({
          id: 'ai_extensions.overview.extension1.feature4',
          message: 'Smart widgets for email, video, and more'
        })
      ],
      link: '/funblocks-ai-assistant',
      chromeStoreLink: 'https://chromewebstore.google.com/detail/funblocks-ai-assistant-%E2%80%93/coodnehmocjfaandkbeknihiagfccoid',
      image: '/img/portfolio/fullsize/ai_reading_en.png',
      badgeColor: '#4CAF50'
    },
    {
      id: 'ai-mindmap-generator',
      badge: 'FOCUSED',
      icon: '/img/mindmap-icon.png',
      title: translate({
        id: 'ai_extensions.overview.extension2.title',
        message: 'AI MindMap Generator'
      }),
      subtitle: translate({
        id: 'ai_extensions.overview.extension2.subtitle',
        message: 'Transform web content and videos into visual mind maps instantly'
      }),
      description: translate({
        id: 'ai_extensions.overview.extension2.description',
        message: 'Specialized extension focused on helping users read and understand web content and YouTube videos by generating visual mind maps, brainstorming, and critical analysis.'
      }),
      features: [
        translate({
          id: 'ai_extensions.overview.extension2.feature1',
          message: 'One-click web page mind mapping'
        }),
        translate({
          id: 'ai_extensions.overview.extension2.feature2',
          message: 'YouTube video transcript analysis'
        }),
        translate({
          id: 'ai_extensions.overview.extension2.feature3',
          message: 'AI-powered brainstorming and analysis'
        }),
        translate({
          id: 'ai_extensions.overview.extension2.feature4',
          message: 'Direct integration with FunBlocks AIFlow'
        })
      ],
      link: '/ai-mindmap',
      chromeStoreLink: 'https://chromewebstore.google.com/detail/ai-prompt-optimizer-refin/kkcpamahgbfihneanpjomblnbnnfnnjh',
      image: '/img/portfolio/fullsize/ai-mindmap-extension-demo.png',
      badgeColor: '#2196F3'
    },
    {
      id: 'ai-prompt-optimizer',
      badge: 'SPECIALIZED',
      icon: '/img/prompt-optimizer-icon.png',
      title: translate({
        id: 'ai_extensions.overview.extension3.title',
        message: 'AI Prompt Optimizer'
      }),
      subtitle: translate({
        id: 'ai_extensions.overview.extension3.subtitle',
        message: 'Enhance AI conversations with prompt optimization and critical thinking'
      }),
      description: translate({
        id: 'ai_extensions.overview.extension3.description',
        message: 'Specialized tool for improving AI interactions across ChatGPT, Claude, Gemini, and other AI platforms with prompt optimization, critical analysis, and thinking enhancement features.'
      }),
      features: [
        translate({
          id: 'ai_extensions.overview.extension3.feature1',
          message: 'One-click prompt optimization'
        }),
        translate({
          id: 'ai_extensions.overview.extension3.feature2',
          message: 'Critical thinking assistant'
        }),
        translate({
          id: 'ai_extensions.overview.extension3.feature3',
          message: 'Related questions and topics generation'
        }),
        translate({
          id: 'ai_extensions.overview.extension3.feature4',
          message: 'Multi-platform AI support'
        })
      ],
      link: '/prompt-optimizer',
      chromeStoreLink: 'https://chromewebstore.google.com/detail/ai-mindmap-mind-mapping-g/nlalnbdblcdgnammbelmmngehcloildo',
      image: '/img/portfolio/fullsize/prompt_optimizer_hero.png',
      badgeColor: '#FF9800'
    }
  ];

  return (
    <section id="extensions-overview" className={extensionsStyles.overviewSection}>
      <div className="container">
        <div className={extensionsStyles.sectionHeading}>
          <Heading as="h2" className={extensionsStyles.sectionTitle}>
            <Translate id="ai_extensions.overview.title">Our Browser Extensions</Translate>
          </Heading>
          <p className={extensionsStyles.sectionDescription}>
            <Translate id="ai_extensions.overview.description">
              Choose the perfect AI extension for your needs. Each extension is designed for specific use cases while maintaining seamless integration with the FunBlocks AI ecosystem.
            </Translate>
          </p>
        </div>

        <div className={extensionsStyles.extensionsGrid}>
          {extensions.map((extension) => (
            <div key={extension.id} className={extensionsStyles.extensionCard}>
              <div className={extensionsStyles.extensionImageWrapper}>
                <img
                  className={extensionsStyles.extensionImage}
                  onClick={() => setShowImageSrc(extension.image)}
                  alt={extension.title}
                  src={extension.image}
                />
                <div
                  className={extensionsStyles.extensionBadge}
                  style={{ backgroundColor: extension.badgeColor }}
                >
                  {extension.badge}
                </div>
              </div>

              <div className={extensionsStyles.extensionContent}>
                <Heading as="h3" className={extensionsStyles.extensionTitle}>
                  {/* <span className={extensionsStyles.extensionIcon}>{extension.icon}</span> */}
                  <div style={{
                    width: 32,
                    height: 32
                  }}>

                    <img
                      src={extension.icon}
                    />
                  </div>

                  {extension.title}
                </Heading>
                <p className={extensionsStyles.extensionSubtitle}>
                  {extension.subtitle}
                </p>
                <p className={extensionsStyles.extensionDescription}>
                  {extension.description}
                </p>

                <ul className={extensionsStyles.extensionFeatures}>
                  {extension.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className={extensionsStyles.extensionFeature}>
                      <span className={extensionsStyles.featureIcon}>✓</span>
                      {feature}
                    </li>
                  ))}
                </ul>

                <div className={extensionsStyles.extensionButtons}>
                  <Link
                    className={clsx('button', extensionsStyles.extensionButtonSecondary)}
                    to={extension.link}
                  >
                    <Translate id="ai_extensions.overview.learn_more">Learn More</Translate>
                  </Link>
                  <Link
                    className={clsx('button', extensionsStyles.extensionButtonPrimary)}
                    href={extension.chromeStoreLink}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Translate id="ai_extensions.overview.install">Install</Translate>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

function ExtensionComparison() {
  return (
    <section className={extensionsStyles.comparisonSection} style={{backgroundColor: 'azure'}}>
      <div className="container">
        <div className={extensionsStyles.sectionHeading}>
          <Heading as="h2" className={extensionsStyles.sectionTitle}>
            <Translate id="ai_extensions.comparison.title">Extension Comparison</Translate>
          </Heading>
          <p className={extensionsStyles.sectionDescription}>
            <Translate id="ai_extensions.comparison.description">
              Understand the differences between our extensions to choose the right one for your needs.
            </Translate>
          </p>
        </div>

        <div className={extensionsStyles.comparisonTable}>
          <div className={extensionsStyles.comparisonHeader}>
            <div className={extensionsStyles.comparisonFeature}>
              <Translate id="ai_extensions.comparison.feature">Feature</Translate>
            </div>
            <div className={extensionsStyles.comparisonExtension}>
              <Translate id="ai_extensions.comparison.funblocks_ai">FunBlocks AI Assistant</Translate>
            </div>
            <div className={extensionsStyles.comparisonExtension}>
              <Translate id="ai_extensions.comparison.mindmap">AI MindMap Generator</Translate>
            </div>
            <div className={extensionsStyles.comparisonExtension}>
              <Translate id="ai_extensions.comparison.prompt_optimizer">AI Prompt Optimizer</Translate>
            </div>
          </div>

          <div className={extensionsStyles.comparisonRow}>
            <div className={extensionsStyles.comparisonFeature}>
              <Translate id="ai_extensions.comparison.writing_assistant">AI Writing Assistant</Translate>
            </div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>❌</div>
            <div className={extensionsStyles.comparisonCell}>❌</div>
          </div>

          <div className={extensionsStyles.comparisonRow}>
            <div className={extensionsStyles.comparisonFeature}>
              <Translate id="ai_extensions.comparison.mindmapping">Mind Mapping</Translate>
            </div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>❌</div>
          </div>

          <div className={extensionsStyles.comparisonRow}>
            <div className={extensionsStyles.comparisonFeature}>
              <Translate id="ai_extensions.comparison.prompt_optimization">Prompt Optimization</Translate>
            </div>
            <div className={extensionsStyles.comparisonCell}>❌</div>
            <div className={extensionsStyles.comparisonCell}>❌</div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
          </div>

          <div className={extensionsStyles.comparisonRow}>
            <div className={extensionsStyles.comparisonFeature}>
              <Translate id="ai_extensions.comparison.critical_thinking">Critical Thinking Tools</Translate>
            </div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
          </div>

          <div className={extensionsStyles.comparisonRow}>
            <div className={extensionsStyles.comparisonFeature}>
              <Translate id="ai_extensions.comparison.contextual_tools">Contextual AI Tools</Translate>
            </div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>Limited</div>
            <div className={extensionsStyles.comparisonCell}>❌</div>
          </div>

          <div className={extensionsStyles.comparisonRow}>
            <div className={extensionsStyles.comparisonFeature}>
              <Translate id="ai_extensions.comparison.aiflow_integration">AIFlow Integration</Translate>
            </div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>❌</div>
          </div>
        </div>

        <div className={extensionsStyles.comparisonNote}>
          <p>
            <Translate id="ai_extensions.comparison.note">
              The FunBlocks AI Assistant includes all features from the AI MindMap Generator plus additional writing and contextual tools. Choose the specialized extensions if you only need specific functionality.
            </Translate>
          </p>
        </div>
      </div>
    </section>
  );
}

export default function AIExtensions() {
  const [showImageSrc, setShowImageSrc] = useState(null);

   function getDomain() {
    if (!window.location.hostname.includes('funblocks')) {
      return 'funblocks.net';
    }
    return window.location.hostname.replace('www.', '');
  }

  function toApp() {
    let url = `https://app.${getDomain()}/#/login?source=flow`;
    openUrl(url);
  }

  return (
    <Layout
      title={translate({
        id: 'ai_extensions.meta.title',
        message: 'FunBlocks AI Browser Extensions - AI-Powered Web Tools'
      })}
      description={translate({
        id: 'ai_extensions.meta.description',
        message: 'Enhance your browsing with FunBlocks AI Extensions: AI Assistant, Mindmap Generator, and Prompt Optimizer. Boost productivity with AI-powered reading, writing, and thinking tools.'
      })}
    >
      <ExtensionStructuredData />
      <GoogleAccountAnalytics />

      <ExtensionsHeader setShowImageSrc={setShowImageSrc} />
      <ExtensionsBenefits />
      <ExtensionsOverview setShowImageSrc={setShowImageSrc} />
      <ExtensionComparison />
      <UniqueValueSection />
      <SocialProofSection />

      <FAQSection
        page={'ai_extensions'}
        faqIds={[
          'q1', 'q2', 'q3', 'q4', 'q5', 'q6', 'q7', 'q8',
          'q9', 'q10'
        ]}
      />

      <CTASection toUrl={'#extensions-overview'} page={'ai_extensions'} />
      <Footer />

      {showImageSrc && (
        <ImageModal
          src={showImageSrc}
          onClose={() => setShowImageSrc(null)}
        />
      )}
    </Layout>
  );
}
